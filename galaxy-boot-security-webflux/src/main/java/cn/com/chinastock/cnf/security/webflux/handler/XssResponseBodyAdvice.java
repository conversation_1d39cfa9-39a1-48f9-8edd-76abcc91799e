package cn.com.chinastock.cnf.security.webflux.handler;

import cn.com.chinastock.cnf.security.output.CopiedFastJsonProperties;
import cn.com.chinastock.cnf.security.output.FastJsonFilterUtil;
import cn.com.chinastock.cnf.security.output.FasterJsonFilterUtil;
import org.reactivestreams.Publisher;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import org.springframework.web.util.HtmlUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;

public class XssResponseBodyAdvice implements WebFilter {

    private final CopiedFastJsonProperties fastJsonProperties;

    public XssResponseBodyAdvice(CopiedFastJsonProperties fastJsonProperties) {
        this.fastJsonProperties = fastJsonProperties;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        ServerHttpResponse originalResponse = exchange.getResponse();
        ServerHttpResponseDecorator decoratedResponse = new ServerHttpResponseDecorator(originalResponse) {
            @Override
            public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
                HttpHeaders headers = getHeaders();
                MediaType contentType = headers.getContentType();

                if (body instanceof Flux) {
                    Flux<? extends DataBuffer> fluxBody = (Flux<? extends DataBuffer>) body;
                    return super.writeWith(fluxBody.flatMap(buffer -> {
                        if (contentType != null && contentType.isCompatibleWith(MediaType.APPLICATION_JSON)) {
                            return handleJsonResponse(buffer, headers);
                        } else if (contentType != null && contentType.isCompatibleWith(MediaType.TEXT_HTML)) {
                            return handleTextResponse(buffer);
                        }
                        return Mono.just(buffer);
                    }));
                }
                return super.writeWith(body);
            }

            private Mono<DataBuffer> handleJsonResponse(DataBuffer buffer, HttpHeaders headers) {
                return DataBufferUtils.join(Mono.just(buffer))
                        .map(dataBuffer -> {
                            byte[] bytes = new byte[dataBuffer.readableByteCount()];
                            dataBuffer.read(bytes);
                            DataBufferUtils.release(dataBuffer);
                            String bodyAsString = new String(bytes, StandardCharsets.UTF_8);

                            Object bodyObject = FasterJsonFilterUtil.handleJacksonResponse(bodyAsString);
                            String newBody = FastJsonFilterUtil.handleFastJsonResponse(bodyObject, fastJsonProperties);

                            byte[] newBytes = newBody.getBytes(StandardCharsets.UTF_8);
                            headers.setContentLength(newBytes.length);
                            return getDelegate().bufferFactory().wrap(newBytes);
                        });
            }

            private Mono<DataBuffer> handleTextResponse(DataBuffer buffer) {
                byte[] bytes = new byte[buffer.readableByteCount()];
                buffer.read(bytes);
                DataBufferUtils.release(buffer);
                String bodyAsString = new String(bytes, StandardCharsets.UTF_8);
                String escapedBody = HtmlUtils.htmlEscape(bodyAsString);
                byte[] newBytes = escapedBody.getBytes(StandardCharsets.UTF_8);
                return Mono.just(getDelegate().bufferFactory().wrap(newBytes));
            }
        };
        return chain.filter(exchange.mutate().response(decoratedResponse).build());
    }
} 